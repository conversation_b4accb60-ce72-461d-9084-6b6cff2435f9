// // ignore_for_file: constant_identifier_names

// import 'package:dio/dio.dart';
// import 'package:room_eight/core/utils/app_exports.dart';
// import 'package:pretty_dio_logger/pretty_dio_logger.dart';

// enum RequestType { GET, POST, PUT, DELETE, PATCH, MULTIPART_POST }

// class ApiClient {
//   final Dio _dio;

//   ApiClient()
//     : _dio = Dio(BaseOptions(headers: _buildHeaders()))
//         ..interceptors.add(
//           PrettyDioLogger(
//             requestHeader: true,
//             requestBody: true,
//             responseHeader: true,
//             responseBody: true,
//             request: true,
//             error: true,
//             compact: true,
//             maxWidth: 90,
//           ),
//         );

//   // --------------------------- HEADERS ---------------------------

//   static Map<String, String> _buildHeaders() {
//     final header = <String, String>{'Content-Type': 'application/json'};
//     String? deviceToken = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) ?? '';
//     // String? calleridentifier =
//     //     Prefobj.preferences?.get(Prefkeys.CALLER_IDENTIFIER) ??
//     //     FlavorConfig.instance.env.callerIdentifier;

//     if (deviceToken != null && deviceToken.isNotEmpty) {
//       header['Authorization'] = 'Bearer $deviceToken';
//     }
//     // if (calleridentifier != null && calleridentifier.isNotEmpty) {
//     //   header['CallerIdentifier'] = calleridentifier;
//     // }
//     return header;
//   }

//   // --------------------------- REQUEST METHOD ---------------------------

//   Future<Map<String, dynamic>> request(
//     RequestType type,
//     String path, {
//     Map<String, dynamic>? data,
//     Map<String, dynamic>? multipartData,
//   }) async {
//     try {
//       final Response response = switch (type) {
//         RequestType.GET => await _dio.get(path),
//         RequestType.POST => await _dio.post(path, data: data),
//         RequestType.PUT => await _dio.put(path, data: data),
//         RequestType.DELETE => await _dio.delete(path),
//         RequestType.PATCH => await _dio.patch(path, data: data),
//         RequestType.MULTIPART_POST => await _dio.post(
//           path,
//           data: await _buildMultipartForm(multipartData),
//         ),
//       };

//       return _handleSuccess(response);
//     } on DioException catch (error) {
//       return _handleDioError(error);
//     } catch (e) {
//       rethrow;
//     }
//   }

//   // --------------------------- SUCCESS HANDLERS ---------------------------

//   Map<String, dynamic> _handleSuccess(Response response) {
//     if ([200, 201, 204].contains(response.statusCode)) {
//       return response.data;
//     } else {
//       throw _handleFailure(response);
//     }
//   }

//   // --------------------------- ERROR HANDLERS ---------------------------

//   DioException _handleFailure(Response response) {
//     final code = response.statusCode ?? 0;
//     final responseData = response.data;
//     String message = "Something went wrong";
//     // Improved validation error handling to support multiple formats
//     if (responseData is Map<String, dynamic>) {
//       // Format 1: Direct validationErrors array
//       if (responseData.containsKey('validationErrors') &&
//           responseData['validationErrors'] is List) {
//         final List errors = responseData['validationErrors'];
//         if (errors.isNotEmpty) {
//           // Extract all error messages and join them
//           message = errors
//               .map((e) => e['message'])
//               .where((m) => m != null)
//               .join('');
//           if (message.isEmpty) {
//             message = "Validation error occurred";
//           }
//         }
//       }
//       // Fallback to standard message fields
//       else if (responseData.containsKey('message')) {
//         message = responseData['message'];
//       } else if (responseData.containsKey('title')) {
//         message = responseData['title'];
//       }
//     }

//     // Toast messages based on status code
//     if ([400, 401, 403, 422, 500].contains(code)) {
//       RoomEight.show(message: message, type: ToastificationType.error);
//     } else if ([404, 409].contains(code)) {
//       RoomEight.show(message: message, type: ToastificationType.warning);
//     } else if ([429, 503].contains(code)) {
//       RoomEight.show(message: message, type: ToastificationType.info);
//     } else {
//       RoomEight.show(message: message, type: ToastificationType.error);
//     }

//     return DioException(
//       requestOptions: response.requestOptions,
//       response: response,
//       type: DioExceptionType.badResponse,
//     );
//   }

//   Map<String, dynamic> _handleDioError(DioException error) {
//     if (error.response != null) {
//       throw _handleFailure(error.response!);
//     } else {
//       final message =
//           error.message ?? "Network error. Please check your connection.";
//       RoomEight.show(message: message, type: ToastificationType.error);
//       throw DioException(
//         requestOptions: error.requestOptions,
//         error: error.error,
//         type: DioExceptionType.unknown,
//       );
//     }
//   }

//   // --------------------------- MULTIPART FORM BUILDER ---------------------------

//   Future<FormData> _buildMultipartForm(Map<String, dynamic>? data) async {
//     final formData = FormData();

//     if (data == null || data.isEmpty) return formData;

//     for (final entry in data.entries) {
//       final key = entry.key;
//       final value = entry.value;

//       // Handle single file (like profile_picture)
//       if (key == 'profile_picture' && value is String && value.isNotEmpty) {
//         formData.files.add(MapEntry(key, await MultipartFile.fromFile(value)));
//       }
//       // Handle multiple files (like profile_pictures) - send as multiple files with same key
//       else if (key == 'profile_pictures' && value is List && value.isNotEmpty) {
//         for (final path in value) {
//           if (path is String && path.isNotEmpty) {
//             // Use same key for multiple files: profile_pictures, profile_pictures, profile_pictures
//             formData.files.add(
//               MapEntry(key, await MultipartFile.fromFile(path)),
//             );
//           }
//         }
//       }
//       // Handle other list fields that might contain file paths
//       else if (value is List &&
//           value.isNotEmpty &&
//           value.first is String &&
//           key != 'profile_pictures') {
//         for (final path in value) {
//           if (path is String && path.isNotEmpty) {
//             formData.files.add(
//               MapEntry(key, await MultipartFile.fromFile(path)),
//             );
//           }
//         }
//       }
//       // Handle regular form fields
//       else if (value != null) {
//         formData.fields.add(MapEntry(key, value.toString()));
//       }
//     }

//     return formData;
//   }
// }

// ignore_for_file: constant_identifier_names

import 'package:dio/dio.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

enum RequestType { GET, POST, PUT, DELETE, PATCH, MULTIPART_POST }

class ApiClient {
  final Dio _dio;

  ApiClient()
    : _dio = Dio(BaseOptions(headers: _buildHeaders()))
        ..interceptors.add(
          PrettyDioLogger(
            requestHeader: true,
            requestBody: true,
            responseHeader: true,
            responseBody: true,
            request: true,
            error: true,
            compact: true,
            maxWidth: 90,
          ),
        );

  // --------------------------- HEADERS ---------------------------

  static Map<String, String> _buildHeaders() {
    final header = <String, String>{'Content-Type': 'application/json'};
    String? deviceToken = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN) ?? '';
    // String? calleridentifier =
    //     Prefobj.preferences?.get(Prefkeys.CALLER_IDENTIFIER) ??
    //     FlavorConfig.instance.env.callerIdentifier;

    if (deviceToken != null && deviceToken.isNotEmpty) {
      header['Authorization'] = 'Bearer $deviceToken';
    }
    // if (calleridentifier != null && calleridentifier.isNotEmpty) {
    //   header['CallerIdentifier'] = calleridentifier;
    // }
    return header;
  }

  // --------------------------- REQUEST METHOD ---------------------------

  Future<Map<String, dynamic>> request(
    RequestType type,
    String path, {
    Map<String, dynamic>? data,
    Map<String, dynamic>? multipartData,
  }) async {
    try {
      final Response response = switch (type) {
        RequestType.GET => await _dio.get(path),
        RequestType.POST => await _dio.post(path, data: data),
        RequestType.PUT => await _dio.put(path, data: data),
        RequestType.DELETE => await _dio.delete(path),
        RequestType.PATCH => await _dio.patch(path, data: data),
        RequestType.MULTIPART_POST => await _dio.post(
          path,
          data: await _buildMultipartForm(multipartData),
        ),
      };

      return _handleSuccess(response);
    } on DioException catch (error) {
      return _handleDioError(error);
    } catch (e) {
      rethrow;
    }
  }

  // --------------------------- SUCCESS HANDLERS ---------------------------

  Map<String, dynamic> _handleSuccess(Response response) {
    if ([200, 201, 204].contains(response.statusCode)) {
      return response.data;
    } else {
      throw _handleFailure(response);
    }
  }

  // --------------------------- ERROR HANDLERS ---------------------------

  DioException _handleFailure(Response response) {
    final code = response.statusCode ?? 0;
    final responseData = response.data;
    String message = "Something went wrong";
    // Improved validation error handling to support multiple formats
    if (responseData is Map<String, dynamic>) {
      // Format 1: Direct validationErrors array
      if (responseData.containsKey('validationErrors') &&
          responseData['validationErrors'] is List) {
        final List errors = responseData['validationErrors'];
        if (errors.isNotEmpty) {
          // Extract all error messages and join them
          message = errors
              .map((e) => e['message'])
              .where((m) => m != null)
              .join('');
          if (message.isEmpty) {
            message = "Validation error occurred";
          }
        }
      }
      // Fallback to standard message fields
      else if (responseData.containsKey('message')) {
        message = responseData['message'];
      } else if (responseData.containsKey('title')) {
        message = responseData['title'];
      }
    }

    // Toast messages based on status code
    if ([400, 401, 403, 422, 500].contains(code)) {
      RoomEight.show(message: message, type: ToastificationType.error);
    } else if ([404, 409].contains(code)) {
      RoomEight.show(message: message, type: ToastificationType.warning);
    } else if ([429, 503].contains(code)) {
      RoomEight.show(message: message, type: ToastificationType.info);
    } else {
      RoomEight.show(message: message, type: ToastificationType.error);
    }

    return DioException(
      requestOptions: response.requestOptions,
      response: response,
      type: DioExceptionType.badResponse,
    );
  }

  Map<String, dynamic> _handleDioError(DioException error) {
    if (error.response != null) {
      throw _handleFailure(error.response!);
    } else {
      final message =
          error.message ?? "Network error. Please check your connection.";
      RoomEight.show(message: message, type: ToastificationType.error);
      throw DioException(
        requestOptions: error.requestOptions,
        error: error.error,
        type: DioExceptionType.unknown,
      );
    }
  }

  // --------------------------- MULTIPART FORM BUILDER ---------------------------

  Future<FormData> _buildMultipartForm(Map<String, dynamic>? data) async {
    final formData = FormData();

    if (data == null || data.isEmpty) return formData;

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is List && value.isNotEmpty && value.first is String) {
        for (final path in value) {
          formData.files.add(MapEntry(key, await MultipartFile.fromFile(path)));
        }
      } else {
        formData.fields.add(MapEntry(key, value.toString()));
      }
    }

    return formData;
  }

  Future<Map<String, dynamic>> multipartRequestWithMixedData({
    required String path,
    required String singleImageKey,
    required String singleImagePath,
    required String multiImageKey,
    required List<String> multiImagePaths,
    required Map<String, dynamic> data,
  }) async {
    try {
      final formData = FormData();

      // Add single image
      formData.files.add(
        MapEntry(singleImageKey, await MultipartFile.fromFile(singleImagePath)),
      );

      // Add multiple images
      for (final path in multiImagePaths) {
        formData.files.add(
          MapEntry(multiImageKey, await MultipartFile.fromFile(path)),
        );
      }

      // Add text fields
      for (final entry in data.entries) {
        formData.fields.add(MapEntry(entry.key, entry.value));
      }

      final response = await _dio.post(path, data: formData);
      return _handleSuccess(response);
    } on DioException catch (error) {
      return _handleDioError(error);
    } catch (e) {
      rethrow;
    }
  }
}
